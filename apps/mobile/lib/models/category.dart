import 'package:freezed_annotation/freezed_annotation.dart';

part 'category.freezed.dart';
part 'category.g.dart';

@freezed
class Category with _$Category {
  const factory Category({
    required String id,
    required String name,
    required CategoryType type,
    String? parentId,
    String? icon,
    String? color,
    @Default(false) bool isDefault,
    @Default(false) bool isSystem,
    @Default(false) bool isArchived,
    @Default(0) int displayOrder,
    required DateTime createdAt,
    required DateTime updatedAt,

    // Optional fields for hierarchical display
    List<Category>? children,
    Category? parent,
  }) = _Category;

  const Category._();

  factory Category.fromJson(Map<String, dynamic> json) {
    // Handle missing fields gracefully for partial category data (e.g., from transaction queries)
    final processedJson = Map<String, dynamic>.from(json);

    // Provide defaults for missing fields
    processedJson['isDefault'] ??= false;
    processedJson['isSystem'] ??= false;
    processedJson['isArchived'] ??= false;
    processedJson['displayOrder'] ??= 0;
    processedJson['createdAt'] ??= DateTime.now().toIso8601String();
    processedJson['updatedAt'] ??= DateTime.now().toIso8601String();

    // Handle type conversion
    if (processedJson['type'] is String) {
      processedJson['type'] = CategoryTypeConverter.fromJson(processedJson['type']);
    }

    // Handle date parsing
    if (processedJson['createdAt'] is String) {
      processedJson['createdAt'] = _parseDateTime(processedJson['createdAt']);
    }
    if (processedJson['updatedAt'] is String) {
      processedJson['updatedAt'] = _parseDateTime(processedJson['updatedAt']);
    }

    return _$CategoryFromJson(processedJson);
  }

  // Helper getters
  bool get isIncome => type == CategoryType.income;
  bool get isExpense => type == CategoryType.expense;
  bool get hasParent => parentId != null;
  bool get hasChildren => children != null && children!.isNotEmpty;
  bool get isUserCreated => !isSystem && !isDefault;
}

extension CategoryEnumHelpers on Category {
 CategoryType get categoryType => type;

 int get colorValue {
   if (color == null) return 0xFF6B7280; // Default gray color

   String colorStr = color!;
   if (colorStr.startsWith('#')) {
     colorStr = colorStr.substring(1);
   }
   if (colorStr.length == 6) {
     colorStr = 'FF$colorStr'; // Add alpha if missing
   }

   return int.tryParse(colorStr, radix: 16) ?? 0xFF6B7280;
 }
}

enum CategoryType {
 @JsonValue('INCOME')
 income,
 @JsonValue('EXPENSE')
 expense,
}

class CategoryTypeConverter {
  static CategoryType fromJson(String value) {
    switch (value.toUpperCase()) {
      case 'INCOME':
        return CategoryType.income;
      case 'EXPENSE':
        return CategoryType.expense;
      default:
        throw ArgumentError('Unknown CategoryType: $value');
    }
  }

  static String toJson(CategoryType type) {
    switch (type) {
      case CategoryType.income:
        return 'INCOME';
      case CategoryType.expense:
        return 'EXPENSE';
    }
  }
}

// Helper function for robust date parsing
DateTime _parseDateTime(dynamic dateValue) {
  if (dateValue == null) {
    return DateTime.now();
  }
  
  if (dateValue is String) {
    // Try parsing as ISO string first
    try {
      return DateTime.parse(dateValue);
    } catch (e) {
      // If that fails, try parsing as timestamp
      final timestamp = int.tryParse(dateValue);
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
      // Fallback to current time
      return DateTime.now();
    }
  }
  
  if (dateValue is int) {
    return DateTime.fromMillisecondsSinceEpoch(dateValue);
  }
  
  // Fallback to current time
  return DateTime.now();
} 