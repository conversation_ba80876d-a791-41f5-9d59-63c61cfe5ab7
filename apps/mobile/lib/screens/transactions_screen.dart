import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:budapp/models/transaction.dart';
import 'package:budapp/providers/transactions_provider.dart';
import 'package:budapp/config/design_tokens.dart';

class TransactionsScreen extends ConsumerStatefulWidget {
  final TransactionFilter? initialFilter;
  final String? customTitle;
  final bool showAddButton;
  
  const TransactionsScreen({
    super.key,
    this.initialFilter,
    this.customTitle,
    this.showAddButton = true,
  });

  @override
  ConsumerState<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends ConsumerState<TransactionsScreen> {
  final _searchController = TextEditingController();
  late TransactionFilter _currentFilter;
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    _currentFilter = widget.initialFilter ?? const TransactionFilter();
    
    // Set search controller if initial filter has search
    if (_currentFilter.search != null) {
      _searchController.text = _currentFilter.search!;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  String get _screenTitle {
    if (widget.customTitle != null) {
      return widget.customTitle!;
    }
    
    // Generate dynamic title based on filter
    if (_currentFilter.accountId != null) {
      return 'Account Transactions';
    } else if (_currentFilter.categoryId != null) {
      return 'Category Transactions';
    }
    
    return 'Transactions';
  }

  @override
  Widget build(BuildContext context) {
    final transactionsAsync = ref.watch(filteredTransactionsProvider(_currentFilter));

    return Scaffold(
      appBar: AppBar(
        title: Text(_screenTitle),
        actions: [
          IconButton(
            icon: Icon(_showFilters ? Icons.filter_list_off : Icons.filter_list),
            onPressed: () {
              setState(() {
                _showFilters = !_showFilters;
              });
            },
          ),
          if (widget.showAddButton)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => context.push('/add-transaction'),
            ),
        ],
      ),
      body: Column(
        children: [
          if (_showFilters) _buildFilterSection(),
          _buildSearchBar(),
          Expanded(
            child: transactionsAsync.when(
              data: (transactions) => _buildTransactionsList(transactions),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorState(error),
            ),
          ),
        ],
      ),
      floatingActionButton: widget.showAddButton ? FloatingActionButton(
        onPressed: () => context.push('/add-transaction'),
        child: const Icon(Icons.add),
      ) : null,
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search transactions...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _updateFilter(_currentFilter.copyWith(search: null));
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppBorderRadius.md),
          ),
        ),
        onChanged: (value) {
          _updateFilter(_currentFilter.copyWith(
            search: value.isEmpty ? null : value,
          ));
        },
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filters',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Wrap(
            spacing: AppSpacing.sm,
            runSpacing: AppSpacing.sm,
            children: [
              _buildTransactionTypeFilter(),
              _buildStatusFilter(),
              _buildDateRangeFilter(),
            ],
          ),
          if (_hasActiveFilters()) ...[
            const SizedBox(height: AppSpacing.sm),
            TextButton(
              onPressed: _clearFilters,
              child: const Text('Clear Filters'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTransactionTypeFilter() {
    return DropdownButton<TransactionType?>(
      value: _currentFilter.type,
      hint: const Text('Type'),
      items: [
        const DropdownMenuItem<TransactionType?>(
          value: null,
          child: Text('All Types'),
        ),
        ...TransactionType.values.map((type) => DropdownMenuItem(
          value: type,
          child: Text(type.displayName),
        )),
      ],
      onChanged: (type) {
        _updateFilter(_currentFilter.copyWith(type: type));
      },
    );
  }

  Widget _buildStatusFilter() {
    return DropdownButton<TransactionStatus?>(
      value: _currentFilter.status,
      hint: const Text('Status'),
      items: [
        const DropdownMenuItem<TransactionStatus?>(
          value: null,
          child: Text('All Statuses'),
        ),
        ...TransactionStatus.values.map((status) => DropdownMenuItem(
          value: status,
          child: Text(status.displayName),
        )),
      ],
      onChanged: (status) {
        _updateFilter(_currentFilter.copyWith(status: status));
      },
    );
  }

  Widget _buildDateRangeFilter() {
    return TextButton.icon(
      icon: const Icon(Icons.date_range),
      label: Text(_getDateRangeText()),
      onPressed: _showDateRangePicker,
    );
  }

  String _getDateRangeText() {
    if (_currentFilter.startDate != null && _currentFilter.endDate != null) {
      final formatter = DateFormat('MMM d');
      return '${formatter.format(_currentFilter.startDate!)} - ${formatter.format(_currentFilter.endDate!)}';
    }
    return 'Date Range';
  }

  Future<void> _showDateRangePicker() async {
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _currentFilter.startDate != null && _currentFilter.endDate != null
          ? DateTimeRange(
              start: _currentFilter.startDate!,
              end: _currentFilter.endDate!,
            )
          : null,
    );

    if (dateRange != null) {
      _updateFilter(_currentFilter.copyWith(
        startDate: dateRange.start,
        endDate: dateRange.end,
      ));
    }
  }

  Widget _buildTransactionsList(List<Transaction> transactions) {
    if (transactions.isEmpty) {
      return _buildEmptyState();
    }

    // Group transactions by date
    final groupedTransactions = _groupTransactionsByDate(transactions);

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(transactionsProvider().notifier).refresh();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(AppSpacing.sm),
        itemCount: groupedTransactions.length,
        itemBuilder: (context, index) {
          final entry = groupedTransactions.entries.elementAt(index);
          final date = entry.key;
          final dayTransactions = entry.value;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDateHeader(date, dayTransactions),
              ...dayTransactions.map((transaction) => _buildTransactionCard(transaction)),
              const SizedBox(height: AppSpacing.sm),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDateHeader(DateTime date, List<Transaction> transactions) {
    final formatter = DateFormat('EEEE, MMMM d, y');
    final totalAmount = transactions.fold<double>(
      0,
      (sum, transaction) {
        switch (transaction.type) {
          case TransactionType.income:
            return sum + transaction.amount;
          case TransactionType.expense:
            return sum - transaction.amount;
          case TransactionType.transfer:
            return sum; // Transfers don't affect the daily total
        }
      },
    );

    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            formatter.format(date),
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            _formatCurrency(totalAmount),
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: totalAmount >= 0 ? AppColors.success : AppColors.error,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionCard(Transaction transaction) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpacing.sm,
        vertical: AppSpacing.xs,
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getTransactionColor(transaction.type).withValues(alpha: 0.1),
          child: Text(
            transaction.type.icon,
            style: const TextStyle(fontSize: 20),
          ),
        ),
        title: Text(
          // Handle nullable description with fallback
          transaction.description ?? '',
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Safely handle nullable account and category fields
            if (transaction.journalLines.isNotEmpty)
              Text(
                transaction.journalLines.first.account?.name ?? 'Unknown Account',
                style: const TextStyle(fontSize: 12),
              ),
            if (transaction.journalLines.isNotEmpty)
              Text(
                transaction.journalLines.first.category?.name ?? 'Uncategorized',
                style: const TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                ),
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              _formatTransactionAmount(transaction),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _getAmountColor(transaction),
                fontSize: 16,
              ),
            ),
            Text(
              DateFormat('h:mm a').format(transaction.date),
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 12,
              ),
            ),
          ],
        ),
        onTap: () => context.push('/transaction/${transaction.id}'),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.receipt_long_outlined,
            size: 64,
            color: AppColors.textDisabled,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'No transactions found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            'Add your first transaction to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textDisabled,
            ),
          ),
          const SizedBox(height: AppSpacing.lg),
          ElevatedButton.icon(
            onPressed: () => context.push('/add-transaction'),
            icon: const Icon(Icons.add),
            label: const Text('Add Transaction'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: AppSpacing.md),
          Text(
            'Error loading transactions',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: AppSpacing.sm),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppSpacing.lg),
          ElevatedButton(
            onPressed: () => ref.read(transactionsProvider().notifier).refresh(),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Map<DateTime, List<Transaction>> _groupTransactionsByDate(List<Transaction> transactions) {
    final grouped = <DateTime, List<Transaction>>{};
    
    for (final transaction in transactions) {
      final date = DateTime(
        transaction.date.year,
        transaction.date.month,
        transaction.date.day,
      );
      
      grouped.putIfAbsent(date, () => []).add(transaction);
    }
    
    // Sort by date (newest first)
    final sortedEntries = grouped.entries.toList()
      ..sort((a, b) => b.key.compareTo(a.key));
    
    return Map.fromEntries(sortedEntries);
  }

  Color _getTransactionColor(TransactionType type) {
    switch (type) {
      case TransactionType.income:
        return AppColors.success;
      case TransactionType.expense:
        return AppColors.error;
      case TransactionType.transfer:
        return AppColors.info;
    }
  }

  Color _getAmountColor(Transaction transaction) {
    switch (transaction.type) {
      case TransactionType.income:
        return AppColors.success;
      case TransactionType.expense:
        return AppColors.error;
      case TransactionType.transfer:
        return AppColors.info;
    }
  }

  String _formatTransactionAmount(Transaction transaction) {
    final sign = transaction.type == TransactionType.income ? '+' : 
                 transaction.type == TransactionType.expense ? '-' : '';
    return '$sign${_formatCurrency(transaction.amount)}';
  }

  String _formatCurrency(double amount) {
    final formatter = NumberFormat.currency(symbol: '\$', decimalDigits: 2);
    return formatter.format(amount.abs());
  }

  void _updateFilter(TransactionFilter newFilter) {
    setState(() {
      _currentFilter = newFilter;
    });
    // Update the provider with the new filter
    ref.read(filteredTransactionsProvider(newFilter));
  }

  bool _hasActiveFilters() {
    return _currentFilter.type != null ||
           _currentFilter.status != null ||
           _currentFilter.startDate != null ||
           _currentFilter.endDate != null ||
           (_currentFilter.search?.isNotEmpty ?? false);
  }

  void _clearFilters() {
    setState(() {
      _currentFilter = const TransactionFilter();
      _searchController.clear();
    });
    ref.read(transactionsProvider().notifier).refresh();
  }
} 